/*
 * 作者：fugui
 * 文件：App.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */
import * as React from 'react';
import {StyleSheet, Text} from 'react-native';
import { TextInput } from 'react-native';

Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.allowFontScaling = false;
import 'react-native-gesture-handler';
import {SafeAreaProvider, useSafeAreaInsets} from 'react-native-safe-area-context';
import {XWidget} from 'react-native-easy-app';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import { TransitionPresets } from '@react-navigation/stack';

import {LetDevice, PluginLaunchControllerArgus, WebViewPage, IMIStorage} from '../../imilab-rn-sdk';
import {imiThemeManager, colors} from '../../imilab-design-ui';
import IMICommSettingPage from '../../imilab-rn-sdk/components/commpage/IMICommSettingPage';

import CameraPlayerPage from './src/live/CameraPlayerPage';
import AlarmListPage from './src/alarmList/AlarmListPage';
import MsgTrans from './src/alarmList/MsgTrans';
import HouseKeepSetting from './src/setting/HouseKeepSetting';
import MoreSetting from './src/setting/MoreSetting';
import NetInfo from './src/setting/NetInfo';

import IMICameraSettingVC from './src/setting/IMICameraSettingVC';
import ImageSetting from './src/setting/ImageSetting';
import WdrSettingPage from './src/setting/WdrSettingPage';
import SleepPage from './src/setting/SleepPage';
import SdCardSettingPage from './src/setting/SdCardSettingPage';
import PlayBackPage from './src/PlayBackPage';
import PlayBackDualCamera from './src/PlayBackDualCamera';
import AlbumPhotoViewPage from './src/alarmList/AlbumPhotoViewPage';
import AlbumVideoView from './src/alarmList/AlbumVideoView';
import PositionSleepPage from './src/setting/PositionSleepPage';
import NightFunctionPage from './src/setting/NightFunctionPage';

import SdCardNewPage from './src/setting/SdCardNewPage';
import PositionSleepNewPage from './src/setting/PositionSleepNewPage';
import CommonSettingPage from './src/setting/CommonSettingPage';
import CloudStoragePage from './src/cloudStorage/CloudStoragePage';
import HouseKeepOldNativeSetting from './src/setting/HouseKeepOldNativeSetting';
import CameraListPage from './src/cameraList/CameraListPage';
import ImagePreView from './src/cameraList/ImagePreView';
import VideoPreView from './src/cameraList/VideoPreView';
import StorageServicesPage from './src/live/StorageServicesPage';
import CustomerServicePage from './src/setting/CustomerServicePage';
import WIFISettingPage from './src/setting/WIFISettingPage';
import RRCTopView from '../../imilab-design-ui/src/widgets/overlayer/RRCTopView';
import {setCustomText} from '../../imilab-rn-sdk/utils/Utils';
import WarnPushSetting from './src/setting/WarnPushSetting';
import AlarmTmieSetting from './src/setting/AlarmTmieSetting';
import AlarmTmieSelect from './src/setting/AlarmTmieSelect'
import UpgradePage from './src/setting/UpgradePage';
import PhotoView from './src/photoView';
import PlayBackAllList from './src/playBack/allEventLists/index'
import BackTimeLists from './src/playBack/allEventLists/timeLists'
import BackVideo from './src/playBack/allEventLists/video'
import PeopleEventSetting from './src/setting/PeopleEventSetting';
import VehicleDetectionPage from './src/setting/VehicleDetectionPage';
import NonVehicleDetectionPage from './src/setting/NonVehicleDetectionPage';
import VirtualFenceSetting from './src/setting/VirtualFenceSetting';
import PrivacyAreaSetting from './src/setting/PrivacyAreaSetting';
import PrivacyAreaModifyPage from './src/setting/PrivacyAreaModifyPage';
import DetectionAreaSetting from './src/setting/DetectionAreaSetting';
import DetectionAreaModifyPage from './src/setting/DetectionAreaModifyPage';
import VirtualFenceAreaSetting from './src/setting/VirtualFenceAreaSetting';
import FamilyProtectionSetting from './src/setting/FamilyProtectionSetting';
import FamilyProtectionTimeSetting from './src/setting/FamilyProtectionTimeSetting';
import CruiseControlSetting from './src/setting/CruiseControlSetting';
import CruiseTimePeriod from './src/setting/CruiseTimePeriod';
import CommonAngles from './src/setting/CommonAngles';
import AddCommonAngles from './src/setting/AddCommonAngles';
import AudioAlarmSetPage from "./src/setting/AudioAlarmSetPage";
import EffectiveTimeSetPage from "./src/setting/EffectiveTimeSetPage";
import PromptVoiceSetPage from "./src/setting/PromptVoiceSetPage";
import EditPromptVoicePage from "./src/setting/EditPromptVoicePage";
import AlarmEventTypePage from "./src/setting/AlarmEventTypePage";
import EffectTimeConfigPage from "./src/setting/EffectTimeConfigPage";

const CODE_PUSH_KEY = 'bz64oCDWUknr5IHkUjczB9bRwU7366ds_Zt9_';

//修改 React Native 的默认字体
setCustomText();

class App extends React.Component {
  componentDidMount() {
    console.log(1111111111111, LetDevice.model, LetDevice.latestCreateTime);
    this.getActiveTime().then(res => {
      if (!res) {
        IMIStorage.save({
          key: LetDevice.deviceID + 'latestCreateTime',
          data: {
            latestCreateTime: LetDevice.latestCreateTime,
          },
          expires: null,
        });
      } else {
        if (res !== LetDevice.latestCreateTime) {
          IMIStorage.save({
            key: LetDevice.deviceID + 'latestCreateTime',
            data: {
              latestCreateTime: LetDevice.latestCreateTime,
            },
            expires: null,
          });
          IMIStorage.remove({key: LetDevice.deviceID + 'qualityIndex'});
          IMIStorage.remove({key: LetDevice.deviceID + 'alarmDialog'});
          IMIStorage.remove({key: LetDevice.deviceID + 'cameraGuideForZoomed'});
          IMIStorage.remove({key: LetDevice.deviceID+'showSDFormatDialog'});
          IMIStorage.remove({key: LetDevice.deviceID+'showOtaUpgradeDialog'});
          IMIStorage.remove({key: LetDevice.deviceID+'showSleepDialog'});
          IMIStorage.remove({key: LetDevice.deviceID + 'freeDay'});
          IMIStorage.remove({key: LetDevice.deviceID + 'cloudExpireRemainDaysLocal'});
          IMIStorage.remove({key: (LetDevice.deviceID + 'showSDAbnormalDialog').replace(/_/g, '')});
          IMIStorage.remove({key: (LetDevice.deviceID + 'showSDAbnormalDialog').replace(/_/g, '')});
          IMIStorage.remove({key: LetDevice.deviceID+'alarmDialog'});
          IMIStorage.remove({key: LetDevice.deviceID + 'isNeverAlertPowerSaveHint'});
          IMIStorage.remove({key: LetDevice.deviceID + 'isDataUsageWarning'});
          IMIStorage.remove({key: LetDevice.deviceID + 'storageRecordMode'});
        }
      }
    })
  }

  getActiveTime() {
    return new Promise((resolve, reject) => {
      IMIStorage.load({
          key: LetDevice.deviceID+'latestCreateTime',
          autoSync: true,
          syncInBackground: true,
      }).then(res => {
          //布尔类型
          resolve(res.latestCreateTime);
      }).catch(_=> {
          resolve(false)
      });
  });
  }

  render() {
    // this.starPageTag =this.props.hasOwnProperty('starPageTag') ? this.props.starPageTag : 'starPageTag';
    console.log('App----------------------' + JSON.stringify(this.props));
    //console.disableYellowBox = true //去除调试时候弹出的警告
    return (
      <SafeAreaProvider>
        <NavigationContainer>
          <ScreenList props={this.props} />

          <RRCTopView />
        </NavigationContainer>
      </SafeAreaProvider>
    );
  }
}
export default App;

function ScreenList(nativeInitialParams) {
  console.log('App---------------------- ScreenList' + JSON.stringify(nativeInitialParams));

  global.EdgeInsets = useSafeAreaInsets();
  XWidget.initResource('http://www.baidu.com').initReferenceScreen(375, 677);

  const {Navigator, Screen} = createStackNavigator();
  return (
    <Navigator
      initialPage={'PluginLaunchControllerArgus'}
      screenOptions={{...TransitionPresets.SlideFromRightIOS, headerShown: false}}
      cardStyle={{backgroundColor: imiThemeManager.theme.pageBg}}>
      <Screen
        name="PluginLaunchControllerArgus"
        component={PluginLaunchControllerArgus}
        initialParams={nativeInitialParams.props}
      />

      {/*设备入口 start*/}
      <Screen name="CameraPlayerPage" component={CameraPlayerPage} options={{...TransitionPresets.FadeFromBottomAndroid}}/>
      <Screen name="StorageServicesPage" component={StorageServicesPage} />
      {/*设备入口end*/}
      {/*看护入口 start*/}
      <Screen name="MsgTrans" component={MsgTrans} options={{...TransitionPresets.FadeFromBottomAndroid}}/>
      <Screen name="AlarmListPage" component={AlarmListPage} />
      {/*图片*/}
      <Screen name="AlbumPhotoViewPage" component={AlbumPhotoViewPage} />
      {/*视频*/}
      <Screen name="AlbumVideoView" component={AlbumVideoView} />
      {/*看护入口end*/}

      <Screen name="PlayBackPage" component={PlayBackPage} />
      <Screen name="PlayBackDualCamera" component={PlayBackDualCamera} />
      <Screen name="PlayBackAllList" component={PlayBackAllList} />
      <Screen name="BackTimeLists" component={BackTimeLists} />
      <Screen name="BackVideo" component={BackVideo} />
      {/* 云存储*/}
      <Screen name="CloudStoragePage" component={CloudStoragePage} />
      {/* 预览截图 */}
      <Screen name={'PhotoView'} component={PhotoView} />
      {/*通用设置界面*/}
      <Screen name="IMICommSettingPage" component={IMICommSettingPage} />
      {/*常见问题*/}
      <Screen name="WebViewPage" component={WebViewPage} />
      {/*RN通用设置 暂时不用*/}
      <Screen name="CommonSettingPage" component={CommonSettingPage} />
      {/*SD卡设置*/}
      <Screen name="SdCardSettingPage" component={SdCardSettingPage} />
      <Screen name={'SdCardNewPage'} component={SdCardNewPage} />
      {/*摄像机设置页面*/}
      <Screen name={'IMICameraSettingVC'} component={IMICameraSettingVC} />
      <Screen name={'ImageSetting'} component={ImageSetting} />
      <Screen name={'WdrSettingPage'} component={WdrSettingPage} />
      <Screen name={'SleepPage'} component={SleepPage} />
      {/*声光报警设置页面*/}
      <Screen name={'AudioAlarmSetPage'} component={AudioAlarmSetPage} />
      {/*提示音选择页面*/}
      <Screen name={'PromptVoiceSetPage'} component={PromptVoiceSetPage} />
      {/*触发事件类型页面*/}
      <Screen name={'AlarmEventTypePage'} component={AlarmEventTypePage} />
      {/*有效时间设置页面*/}
      <Screen name={'EffectTimeConfigPage'} component={EffectTimeConfigPage} />
      <Screen name={'PositionSleepPage'} component={PositionSleepPage} />
      <Screen name={'PositionSleepNewPage'} component={PositionSleepNewPage} />
      <Screen name={'NightFunctionPage'} component={NightFunctionPage} />
      <Screen name={'WarnPushSetting'} component={WarnPushSetting} />
      <Screen name={'AlarmTmieSetting'} component={AlarmTmieSetting} />
      <Screen name={'AlarmTmieSelect'} component={AlarmTmieSelect} />

      <Screen name={'UpgradePage'} component={UpgradePage} />

      {/*WiFi设置页面062热点直连模式*/}
      <Screen name={'WIFISettingPage'} component={WIFISettingPage} />

      {/*看护设置页面*/}
      <Screen name={'HouseKeepSetting'} component={HouseKeepSetting} />
      <Screen name={'HouseKeepOldNativeSetting'} component={HouseKeepOldNativeSetting} />

      {/*RN相册*/}
      <Screen name={'CameraListPage'} component={CameraListPage} />
      <Screen name={'ImagePreView'} component={ImagePreView} />
      <Screen name={'VideoPreView'} component={VideoPreView} />
      {/*联系客服*/}
      <Screen name={'CustomerServicePage'} component={CustomerServicePage} />

      {/* 更多设置 */}
      <Screen name={'MoreSetting'} component={MoreSetting}/>
      <Screen name={'NetInfo'} component={NetInfo}/>

      {/* 人形 */}
      <Screen name={'PeopleEventSetting'} component={PeopleEventSetting}/>
      {/* 机动车检测 */}
      <Screen name={'VehicleDetectionPage'} component={VehicleDetectionPage}/>
      {/* 非机动车检测 */}
      <Screen name={'NonVehicleDetectionPage'} component={NonVehicleDetectionPage}/>
      {/* 虚拟围栏 */}
      <Screen name={'VirtualFenceSetting'} component={VirtualFenceSetting}/>
      {/* 虚拟围栏区域编辑 */}
      <Screen name={'VirtualFenceAreaSetting'} component={VirtualFenceAreaSetting}/>
      {/* 隐私区域保护 */}
      <Screen name={'PrivacyAreaSetting'} component={PrivacyAreaSetting}/>
      {/* 隐私区域编辑 */}
      <Screen name={'PrivacyAreaModifyPage'} component={PrivacyAreaModifyPage}/>
      {/* 检测区域绘制 */}
      <Screen name={'DetectionAreaSetting'} component={DetectionAreaSetting}/>
      {/* 检测区域编辑 */}
      <Screen name={'DetectionAreaModifyPage'} component={DetectionAreaModifyPage}/>
        {/* 家人守护 */}
      <Screen name={'FamilyProtectionSetting'} component={FamilyProtectionSetting}/>
      
        {/* 家人守护时间段设置 */} 
      <Screen name={'FamilyProtectionTimeSetting'} component={FamilyProtectionTimeSetting}/>
       {/* 自动巡航  */} 
      <Screen name={'CruiseControlSetting'} component={CruiseControlSetting}/>
        {/* 巡航时间段  */}  
      <Screen name={'CruiseTimePeriod'} component={CruiseTimePeriod}/>
       {/* 常用角度  */}  
      <Screen name={'CommonAngles'} component={CommonAngles}/>
      {/* 添加常用角度  */}  
      <Screen name={'AddCommonAngles'} component={AddCommonAngles}/>
    </Navigator>
  );
}

const styles = StyleSheet.create({
  tab: {
    flex: 1,
    textAlign: 'center',
    color: colors.gray,
    fontSize: 15,
    textShadowColor: imiThemeManager.theme.primaryColor,
  },
});
