import {Text, View, StyleSheet, ScrollView, Image} from 'react-native';
import React, {useEffect, useState} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage, LetDevice} from '../../../../imilab-rn-sdk';
import {imiThemeManager, showLoading, showToast} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import I18n, {stringsTo} from '../../../../globalization/Localize';

const NonVehicleDetectionPage = props => {
  const nonVehicleSwitchId = '100029';
  const nonVehicleDetectionId = '100030';
  const nonVehiclePeopleNearId = '100031';
  const nonVehicleFrameId = '100032';
  const [nonVehicleSwitch, setNonVehicleSwitch] = useState(true);
  const [nonVehicleDetection, setNonVehicleDetection] = useState(true);
  const [nonVehiclePeopleNear, setNonVehiclePeopleNear] = useState(true);
  const [nonVehicleFrame, setNonVehicleFrame] = useState(false);

  useEffect(() => {
    LetDevice.getSingleProperty(nonVehicleSwitchId).then(res => {
      if (res?.value?.code == 0) {
        setNonVehicleSwitch(res.value.value);
      }
    });
    LetDevice.getSingleProperty(nonVehicleDetectionId).then(res => {
      if (res?.value?.code == 0) {
        setNonVehicleDetection(res.value.value);
      }
    });
    LetDevice.getSingleProperty(nonVehiclePeopleNearId).then(res => {
      if (res?.value?.code == 0) {
        setNonVehiclePeopleNear(res.value.value);
      }
    });
    LetDevice.getSingleProperty(nonVehicleFrameId).then(res => {
      if (res?.value?.code == 0) {
        setNonVehicleFrame(res.value.value);
      }
    });
  }, []);

  function handleVehicleChange(value, thingId) {
    showLoading(stringsTo('commWaitText'), true);
    const paramJson = JSON.stringify({msg_id: thingId, value});

    LetDevice.setProperties(true, LetDevice.deviceID, thingId, paramJson)
      .then(() => {
        showToast(stringsTo('settings_set_success'));
        if (thingId === nonVehicleSwitchId) {
          setNonVehicleSwitch(value);
        } else if (thingId === nonVehicleDetectionId) {
          setNonVehicleDetection(value)
        } else if (thingId === nonVehiclePeopleNearId) {
          setNonVehiclePeopleNear(value)
        } else if (thingId === nonVehicleFrameId) {
          setNonVehicleFrame(value)
        }
      })
      .catch(e => {
        showToast(I18n.t('operationFailed'));
        if (thingId === nonVehicleSwitchId) {
          setNonVehicleSwitch(nonVehicleSwitch);
        } else if (thingId === nonVehicleDetectionId) {
          setNonVehicleDetection(nonVehicleDetection)
        } else if (thingId === nonVehiclePeopleNearId) {
          setNonVehiclePeopleNear(nonVehiclePeopleNear)
        } else if (thingId === nonVehicleFrameId) {
          setNonVehicleFrame(nonVehicleFrame)
        }
      })
      .finally(() => {
        showLoading(false);
      });
  }

  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo('non_vehicle_title')}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
               props.navigation.goBack? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'housekeeping_assistant_back',
          },
        ]}
        right={[]}
      />

      <ScrollView style={styles.scrollView}>
        {/* 设置选项区域 */}
        <View style={styles.settingsSection}>
          {/* 检测开关 */}
          <ListItmeWithSwitch
           title={stringsTo("vehicle_title")}
            value={nonVehicleSwitch}
            onValueChange={value => {
              handleVehicleChange(value, nonVehicleSwitchId)
            }}
            accessibilityLabel={['non_vehicle_switch_off', 'non_vehicle_switch_on']}
          />
          {nonVehicleSwitch ? (
            <View>
              {/* 检测到车辆 */}
              <ListItmeWithSwitch
                title={stringsTo("non_vehicle_detection")}
                value={nonVehicleDetection}
                disabled={!nonVehicleSwitch} // 当人形检测关闭时禁用
                onValueChange={value => {
                  handleVehicleChange(value, nonVehicleDetectionId)
                }}
                accessibilityLabel={['non_vehicle_detection_off', 'non_vehicle_detection_on']}
              />
              {/* 有人靠近车辆 */}
              <ListItmeWithSwitch
                title={stringsTo("non_vehicle_people_near")}
                value={nonVehiclePeopleNear}
                disabled={!nonVehicleSwitch} // 当人形检测关闭时禁用
                onValueChange={value => {
                  handleVehicleChange(value, nonVehiclePeopleNearId)
                }}
                accessibilityLabel={['non_vehicle_people_near_off', 'non_vehicle_people_near_on']}
              />
              {/* 车辆画框 */}
              <ListItmeWithSwitch
                title={stringsTo("non_vehicle_frame")}
                subtitle={stringsTo("non_vehicle_frame_hint")}
                value={nonVehicleFrame}
                disabled={!nonVehicleSwitch} // 当人形检测关闭时禁用
                onValueChange={value => {
                  handleVehicleChange(value, nonVehicleFrameId)
                }}
                accessibilityLabel={['non_vehicle_frame_off', 'non_vehicle_frame_on']}
              />
            </View>
          ) : null}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  algorithmSection: {
    backgroundColor: '#F8F9FA',
    marginTop: 0,
  },
  imageContainer: {
    height: 200,
    backgroundColor: '#E8E8E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 15,
    marginTop: 15,
    borderRadius: 8,
    overflow: 'hidden',
  },
  demoImage: {
    width: '100%',
    height: '100%',
  },
  algorithmInfo: {
    padding: 15,
  },
  algorithmTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  algorithmDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  settingsSection: {
    marginTop: 10,
  },
});

export default NonVehicleDetectionPage;
