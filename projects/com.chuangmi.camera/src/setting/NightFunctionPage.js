import React from 'react';

import {StyleSheet, View, Image, Text, TouchableWithoutFeedback, Dimensions, BackHandler} from 'react-native';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';

import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';

import {LetDevice, LetIMIIotRequest} from '../../../../imilab-rn-sdk';
import {showLoading, showToast} from '../../../../imilab-design-ui';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';
import IMIToast from '../../../../imilab-design-ui/src/widgets/IMIToast';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
const screen_width = Dimensions.get('window').width;

/**
 * 夜视功能设置页面
 */

export default class NightFunctionPage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props, context) {
    super(props, context);
    this.isTwoCamera = (DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber || '1') === '2';
    this.state = {
      nightMode: 2,
      fullColor: false
    };
    this.loading1 = false;
    this.loading2 = false;
  }
  componentDidMount() {
    console.log('nightFunctionPage --', this.props.route.params.nightValue);
    if (this.props.route.params?.nightValue !== undefined) {
      this.setState({
        nightMode: this.props.route.params.nightValue,
      });
    }
    this.getNightState();
  }
  // 更新夜视状态
  getNightState = () => {
    // 使用接口获取哦
    setTimeout(() => {
      showLoading(stringsTo('commLoadingText'), true);
    })
    const params = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10003,
        method: 'sync'
      },
      Method: 'POST',
    };
    const params3 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10005,
        method: 'sync'
      },
      Method: 'POST',
    };
    this.loading1 = true;
    this.loading2 = true;

    // 双摄设备不请求微光全彩开关数据
    if (!this.isTwoCamera) {
      LetIMIIotRequest.sendUserServerRequest(params3, true).then(data3 => {
        this.setState({
          fullColor: data3?.value?.value,
        });
      }).catch(error => {
      }).finally(() => {
        this.loading1 = false;
        if (!this.loading2) {
          showLoading(false);
        }
      })
    } else {
      this.loading1 = false;
    }
    LetIMIIotRequest.sendUserServerRequest(params, true).then(value => {
        console.log('夜视模式getPropertyCloud$$$$$$$$$$$$' + value);
        console.log('getPropertyCloud$$$$$$$$$$$$' + value);

        this.setState({
          nightMode: value?.value?.value,
        });
      })
      .catch(error => {
        showToast(I18n.t('commLoadingFailText'));
        console.log('夜视模式', JSON.stringify(error));
      }).finally(() => {
        this.loading2 = false;
        if (!this.loading1) {
          showLoading(false);
        }
      });
  };

  UNSAFE_componentWillMount() {
    if (isAndroid()) {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }
  }
  componentWillUnmount() {
    if (isAndroid()) {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
    showLoading(false);
  }

  onBackHandler = () => {
    // console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
    // if (navigation.isFocused()) {
      this._onPressBack();
      return true;
    // } else {
    // }
    // return false;
  };

  // 返回上一页
  _onPressBack = () => {
    if (this.props.route.params.callback) {
      console.log('返回传值', this.state.nightMode);
      let tempStr;

      if (this.isTwoCamera) {
        // 双摄设备的映射
        if (this.state.nightMode == 3) {
          // 全彩夜视
          tempStr = stringsTo('dual_camera_full_color_title');
        } else if (this.state.nightMode == 2) {
          // 黑白夜视
          tempStr = stringsTo('dual_camera_black_white_title');
        } else if (this.state.nightMode == 4) {
          // 智能夜视
          tempStr = stringsTo('dual_camera_smart_title');
        }
      } else {
        // 单摄设备的映射
        if (this.state.nightMode == 1) {
          //黑白夜视 改成打开
          tempStr = stringsTo('full_color_vision_title');
        } else if (this.state.nightMode == 0) {
          // 全彩夜视 改成关闭
          tempStr = stringsTo('black_white_vision_title'); // 已开启
        } else if (this.state.nightMode == 2) {
          // 智能夜视 改成自动
          tempStr = stringsTo('fullColor_smart_tit');
        }
      }

      console.log('返回值---', tempStr);
      this.props.route.params.callback(tempStr);
    }
    this.props.navigation.pop();
  };

  render() {
    return (
      <View style={styles.container}>
        <NavigationBar
          title={I18n.t('setttings_infared')}
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this._onPressBack()}]}
          right={[]}
        />
        <View style={{width: '100%', height: 210}}>
          {this._renderItemOne()}
          {this._renderItemTwo()}
          {this._renderItemThe()}
        </View>

        {/* 双摄设备智能夜视说明图片 */}
        {this.isTwoCamera && this._renderSmartNightVisionImages()}

        {/* <View style={{top: 20, height: 130, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-around'}}>
          {[
            {text: I18n.t('full_color_vision_title'), uri: require('../../resources/images/full_color_img.png')},
            {
              text: I18n.t('black_white_vision_title'),
              uri: require('../../resources/images/full_color_black_white_img.png'),
            },
          ].map((item, i) => {
            return (
              <View key={i} style={{width: 145, height: 65}}>
                <Image key={i} style={{width: 145, height: 65, borderRadius: 10}} source={item.uri} />
                <Text style={{fontSize: 10, color: '#828282', top: 5, lineHeight: 15}}>{item.text}</Text>
              </View>
            );
          })}
        </View> */}
        {!this.isTwoCamera && <View style={{backgroundColor: 'rgba(0,0,0,0.15)', height: 1,marginLeft:14,
            marginRight:14,marginBottom:20,marginTop:20}} />}
        {!this.isTwoCamera && <ListItmeWithSwitch
              disabled={this.state.nightMode == 0}
              title={stringsTo('settings_light_full_color_title')}
              /*  hide={!showLightFullColor} 记得打开 todo*/
              subtitle={stringsTo('settings_light_full_color_subtitle')}
              value={this.state.fullColor}
              onValueChange={value => {
                const params = {value};
                IMIToast.hideToast()
                showLoading(stringsTo('commWaitText'), true);
                LetDevice.setProperties(true, LetDevice.deviceID, '10005', JSON.stringify(params))
                  .then(() => {
                    showToast(stringsTo('settings_set_success'))
                    this.setState({
                      fullColor: value,
                    });
                  })
                  .catch(error => {
                    console.log(error, '错误了');
                    this.setState({
                      fullColor: this.state.fullColor,
                    });
                    showToast(I18n.t('operationFailed'));
                  })
                  .finally(() => {
                    showLoading(false);
                  });
              }}
              accessibilityLabel={['camera_full_color_off', 'camera_full_color_on']}
            />}
      </View>
    );
  }
  _renderItemOne() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            console.log(this.isTwoCamera ? '全彩夜视' : '自动切换');
            // 双摄设备：全彩夜视=3，单摄设备：自动切换=2
            this._onSelectedItem(this.isTwoCamera ? 3 : 2);
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.nightMode == (this.isTwoCamera ? 3 : 2) ? '#12AA9C' : '#000000',
                }}>
                {this.isTwoCamera ? stringsTo('dual_camera_full_color_title') : stringsTo('fullColor_title3')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: this.state.nightMode == (this.isTwoCamera ? 3 : 2) ? '#12AA9C' : '#828282',
                }}>
                {this.isTwoCamera ? stringsTo('dual_camera_full_color_subtitle') : stringsTo('fullColor_subTit')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.nightMode == (this.isTwoCamera ? 3 : 2) ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderItemTwo() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            console.log(this.isTwoCamera ? '黑白夜视' : '一直打开');
            // 双摄设备：黑白夜视=2，单摄设备：一直打开=0
            this._onSelectedItem(this.isTwoCamera ? 2 : 0);
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.nightMode == (this.isTwoCamera ? 2 : 0) ? '#12AA9C' : '#000000',
                }}>
                {this.isTwoCamera ? stringsTo('dual_camera_black_white_title') : stringsTo('fullColor_title2')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: this.state.nightMode == (this.isTwoCamera ? 2 : 0) ? '#12AA9C' : '#828282',
                }}>
                {this.isTwoCamera ? stringsTo('dual_camera_black_white_subtitle') : stringsTo('fullColor_black_subTit')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.nightMode == (this.isTwoCamera ? 2 : 0) ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _renderItemThe() {
    return (
      <View style={{width: '100%', height: 70, backgroundColor: 'white'}}>
        <TouchableWithoutFeedback
          onPress={_ => {
            console.log(this.isTwoCamera ? '智能夜视' : '一直关闭');
            // 双摄设备：智能夜视=4，单摄设备：一直关闭=1
            this._onSelectedItem(this.isTwoCamera ? 4 : 1);
          }}>
          <View style={{flexDirection: 'column', height: 70, backgroundColor: 'white'}}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  marginLeft: 14,
                  marginTop: 8,
                  lineHeight: 22,
                  width: screen_width - 14 * 3 - 25,
                  flexGrow: 1,
                  fontSize: 15,
                  textAlign: 'left',
                  justifyContent: 'center',
                  color: this.state.nightMode == (this.isTwoCamera ? 4 : 1) ? '#12AA9C' : '#000000',
                }}>
                {this.isTwoCamera ? stringsTo('dual_camera_smart_title') : stringsTo('fullColor_title1')}
              </Text>
              <Text
                numberOfLines={2}
                style={{
                  marginLeft: 14,
                  height: 32,
                  width: screen_width - 14 * 3 - 25,
                  fontSize: 12,
                  textAlign: 'left',
                  color: this.state.nightMode == (this.isTwoCamera ? 4 : 1) ? '#12AA9C' : '#828282',
                }}>
                {this.isTwoCamera ? stringsTo('dual_camera_smart_subtitle') : stringsTo('fullColor_smart_subTit')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', position: 'absolute', flex: 1, right: 14}}>
              <Image
                style={{
                  marginTop: 22.5,
                  height: 25,
                  width: 25,
                  tintColor: this.state.nightMode == (this.isTwoCamera ? 4 : 1) ? null : 'transparent',
                }}
                source={require('../../resources/images/icon_select_s.png')}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  _onSelectedItem(value) {
    console.log('夜视选中value--', value);

    // this.setState({nightMode: value},callback=>{
    //     this._onPressBack()
    // });
    IMIToast.hideToast()
    showLoading(stringsTo('commWaitText'), true);
    //let params = {DayNightMode: value};
    const params = {msg_id: 10003, value};
    const paramJson = JSON.stringify(params);
    console.log('夜视选中value测试值=-hhhh--', JSON.stringify(params));
    //LetDevice.setProperties(true, LetDevice.deviceID, 10003, paramJson)
    LetDevice.setProperties(true, LetDevice.deviceID, '10003', paramJson)
      .then(() => {
        showLoading(false);
        this.setState({nightMode: value}, callback => {
          // this._onPressBack();
          showToast(stringsTo('settings_set_success'))
        });
      })
      .catch(error => {
        console.log('失败----', error);
        console.log('失败----', error, this.state.nightMode, value);
        this.setState({nightMode: this.state.nightMode});
        showToast(I18n.t('operationFailed'));
        showLoading(false);
      });

    IMILogUtil.uploadClickEventValue({DayNightMode: value.toString()});
  }

  // 渲染双摄设备智能夜视说明图片
  _renderSmartNightVisionImages() {
    const screenWidth = Dimensions.get('window').width;
    const leftMargin = 20;
    const rightMargin = 20;
    const imageGap = 13;
    const availableWidth = screenWidth - leftMargin - rightMargin - imageGap;
    const imageWidth = availableWidth / 2;

    return (
      <View style={{
        marginTop: 20,
        paddingHorizontal: leftMargin,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start'
      }}>
        {/* 全彩夜视图片和文案 */}
        <View style={{
          width: imageWidth,
          alignItems: 'center'
        }}>
          <Image
            source={require('../../resources/images/ic_full_color.webp')}
            style={{
              width: imageWidth,
              height: imageWidth * 0.57, // 保持合适的宽高比
              resizeMode: 'contain'
            }}
          />
          <Text style={{
            marginTop: 14,
            fontSize: 14,
            color: '#333333',
            textAlign: 'center'
          }}>
            全彩夜视
          </Text>
        </View>

        {/* 黑白夜视图片和文案 */}
        <View style={{
          width: imageWidth,
          alignItems: 'center'
        }}>
          <Image
            source={require('../../resources/images/ic_black_white.webp')}
            style={{
              width: imageWidth,
              height: imageWidth * 0.57, // 保持合适的宽高比
              resizeMode: 'contain'
            }}
          />
          <Text style={{
            marginTop: 14,
            fontSize: 14,
            color: '#333333',
            textAlign: 'center'
          }}>
            黑白夜视
          </Text>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
