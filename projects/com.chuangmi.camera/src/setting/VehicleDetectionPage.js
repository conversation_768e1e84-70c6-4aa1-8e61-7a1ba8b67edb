import {Text, View, StyleSheet, ScrollView, Image} from 'react-native';
import React, {useEffect, useState} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage, LetDevice} from '../../../../imilab-rn-sdk';
import {imiThemeManager, showLoading, showToast} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import I18n, {stringsTo} from '../../../../globalization/Localize';

const VehicleDetectionPage = props => {
  const vehicleSwitchId = '100025';
  const vehicleDetectionId = '100026';
  const vehiclePeopleNearId = '100027';
  const vehicleFrameId = '100028';
  const [vehicleSwitch, setVehicleSwitch] = useState(true);
  const [vehicleDetection, setVehicleDetection] = useState(true);
  const [vehiclePeopleNear, setVehiclePeopleNear] = useState(true);
  const [vehicleFrame, setVehicleFrame] = useState(false);

  useEffect(() => {
    LetDevice.getSingleProperty(vehicleSwitchId).then(res => {
      if (res?.value?.code == 0) {
        setVehicleSwitch(res.value.value);
      }
    });
    LetDevice.getSingleProperty(vehicleDetectionId).then(res => {
      if (res?.value?.code == 0) {
        setVehicleDetection(res.value.value);
      }
    });
    LetDevice.getSingleProperty(vehiclePeopleNearId).then(res => {
      if (res?.value?.code == 0) {
        setVehiclePeopleNear(res.value.value);
      }
    });
    LetDevice.getSingleProperty(vehicleFrameId).then(res => {
      if (res?.value?.code == 0) {
        setVehicleFrame(res.value.value);
      }
    });
  }, []);

  function handleVehicleChange(value, thingId) {
    showLoading(stringsTo('commWaitText'), true);
    const paramJson = JSON.stringify({msg_id: thingId, value});

    LetDevice.setProperties(true, LetDevice.deviceID, thingId, paramJson)
      .then(() => {
        showToast(stringsTo('settings_set_success'));
        if (thingId === vehicleSwitchId) {
          setVehicleSwitch(value);
        } else if (thingId === vehicleDetectionId) {
          setVehicleDetection(value)
        } else if (thingId === vehiclePeopleNearId) {
          setVehiclePeopleNear(value)
        } else if (thingId === vehicleFrameId) {
          setVehicleFrame(value)
        }
      })
      .catch(e => {
        showToast(I18n.t('operationFailed'));
        if (thingId === vehicleSwitchId) {
          setVehicleSwitch(vehicleSwitch);
        } else if (thingId === vehicleDetectionId) {
          setVehicleDetection(vehicleDetection)
        } else if (thingId === vehiclePeopleNearId) {
          setVehiclePeopleNear(vehiclePeopleNear)
        } else if (thingId === vehicleFrameId) {
          setVehicleFrame(vehicleFrame)
        }
      })
      .finally(() => {
        showLoading(false);
      });
  }

  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo('vehicle_title')}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
               props.navigation.goBack? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'housekeeping_assistant_back',
          },
        ]}
        right={[]}
      />

      <ScrollView style={styles.scrollView}>
        {/* 设置选项区域 */}
        <View style={styles.settingsSection}>
          {/* 检测开关 */}
          <ListItmeWithSwitch
           title={stringsTo("vehicle_title")}
            value={vehicleSwitch}
            onValueChange={value => {
              handleVehicleChange(value, vehicleSwitchId)
            }}
            accessibilityLabel={['vehicle_switch_off', 'vehicle_switch_on']}
          />
          {vehicleSwitch ? (
            <View>
              {/* 检测到车辆 */}
              <ListItmeWithSwitch
                title={stringsTo("vehicle_detection")}
                value={vehicleDetection}
                disabled={!vehicleSwitch} // 当人形检测关闭时禁用
                onValueChange={value => {
                  handleVehicleChange(value, vehicleDetectionId)
                }}
                accessibilityLabel={['vehicle_detection_off', 'vehicle_detection_on']}
              />
              {/* 有人靠近车辆 */}
              <ListItmeWithSwitch
                title={stringsTo("vehicle_people_near")}
                value={vehiclePeopleNear}
                disabled={!vehicleSwitch} // 当人形检测关闭时禁用
                onValueChange={value => {
                  handleVehicleChange(value, vehiclePeopleNearId)
                }}
                accessibilityLabel={['vehicle_people_near_off', 'vehicle_people_near_on']}
              />
              {/* 车辆画框 */}
              <ListItmeWithSwitch
                title={stringsTo("vehicle_frame")}
                subtitle={stringsTo("vehicle_frame_hint")}
                value={vehicleFrame}
                disabled={!vehicleSwitch} // 当人形检测关闭时禁用
                onValueChange={value => {
                  handleVehicleChange(value, vehicleFrameId)
                }}
                accessibilityLabel={['vehicle_frame_off', 'vehicle_frame_on']}
              />
            </View>
          ) : null}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  algorithmSection: {
    backgroundColor: '#F8F9FA',
    marginTop: 0,
  },
  imageContainer: {
    height: 200,
    backgroundColor: '#E8E8E8',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 15,
    marginTop: 15,
    borderRadius: 8,
    overflow: 'hidden',
  },
  demoImage: {
    width: '100%',
    height: '100%',
  },
  algorithmInfo: {
    padding: 15,
  },
  algorithmTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  algorithmDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  settingsSection: {
    marginTop: 10,
  },
});

export default VehicleDetectionPage;
