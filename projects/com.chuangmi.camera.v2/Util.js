// import { <PERSON><PERSON>, Service, Host } from "miot";
// import API, { CloudApi, CameraDomain } from "./API";
// import DateFormatter from './DateFormater';
// import { PermissionsAndroid, Platform } from 'react-native';
// import { localStrings as LocalizedStrings } from "../MHLocalizableString";
// import { Order } from "../framework/EventLoaderInf";
// import { DarkMode } from 'miot';
// import { CldDldTypes } from '../framework/CloudEventLoader';
// import dayjs from 'dayjs';

const TAG = "Util";

const LangMap = { zh: "zh-cn", zh_hk: "zh-hk", zh_tw: "zh-tw" };

// small icon is not used yet
const PassSmIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const StaySmIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const FaceSmIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const DestroySmIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const BellSmIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const UnKnownFaceSmIc = require("../com.chuangmi.camera/resources/images/icon_album.png");

const ObjectMvIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const PeopleMvIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const DefaltIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const AIIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const BabyCryIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const FaceIc = require("../com.chuangmi.camera/resources/images/icon_album.png");
const UnKnownFaceIc = require("../com.chuangmi.camera/resources/images/icon_album.png");



const EvOrder = {
  "BabyCry": 100,
  "KnownFace": 80,
  "Face": 60,
  "PeopleMotion": 50,
  "ObjectMotion": 30,
  "AI": 20
};

const EvIcoOrder = {
  "BabyCry": 100,
  "KnownFace": 80,
  "Face": 60,
  "PeopleMotion": 50,
  "ObjectMotion": 30,
  "AI": 20
};

const EvMap = {

  'ObjectMotion': { des: "移动", des_with_face: "人脸", icon: { norm: ObjectMvIc, small: BellSmIc }, textActive: "#FDC541" },
  'PeopleMotion': { des: "移动人形", des_with_face: "人脸", icon: { norm: PeopleMvIc, small: PassSmIc }, textActive: "#44CECA" },
  'BabyCry': { des: "宝宝哭", des_with_face: "人脸", icon: { norm: BabyCryIc, small: DestroySmIc }, textActive: "#9B91FF" },
  'Face': { des: "陌生人", des_with_face: "人脸", icon: { norm: UnKnownFaceIc, small: UnKnownFaceSmIc }, textActive: "#ACB2C7" },
  'KnownFace': { des: "人形", des_with_face: "人脸", icon: { norm: FaceIc, small: FaceSmIc }, textActive: "#2DB0FF" },
  'AI': { des: "智能", des_with_face: "人脸", icon: { norm: AIIc, small: StaySmIc }, textActive: "#7DA6E0" },
  'Default': { des: "人形移动", des_with_face: "人脸通过", icon: { norm: DefaltIc, small: null }, textActive: "#4882FF" }
};

export const DayInMilli = 24 * 60 * 60 * 1000;

export default class Util {

  static getLanguage() {
    let lang = Host.locale.language || "en_us";
    if (LangMap[lang]) {
      return LangMap[lang];
    }
    return lang;
  }

  static isAndroid() {
    return Platform.OS == "android";
  }


  static zeroPad(aNum, aBase) {
    let len = (String(aBase).length - String(aNum).length) + 1;
    return len > 0 ? new Array(len).join('0') + aNum : aNum;
  }

  static fmtStr(aFmt, ...args) {
    let jStr = aFmt.replace(/{([0-9]+)}/g,
      function(sub) {
        return args[parseInt(sub[1])];
      });
    return jStr;
  }

  static fmtSize(aSize) {
    let unitArr = new Array("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB");
    let index = Math.floor(Math.log(Math.max(1, aSize)) / Math.log(1024));
    let base = Math.pow(1024, index);
    let size = aSize / base;
    if (aSize % base / base > 0.01) {
      size = size.toFixed(2);// 保留的小数位数
    } else {
      size = size.toFixed(0);
    }

    return size + unitArr[index];
  }

  static concat(aResType, ...arrays) {
    let totalLength = 0;
    for (let arr of arrays) {
      totalLength += arr.length;
    }
    let result = new aResType(totalLength);
    let offset = 0;
    for (let arr of arrays) {
      result.set(arr, offset);
      offset += arr.length;
    }
    return result;
  }

  static isString(aVal) {
    return "string" === typeof aVal;
  }




  static isEarlierOrEqualTo(timeStr1, timeStr2) {
    // 早于 - 返回 -1
    // 晚于 - 返回 1
    // 等于 - 返回 0
    // convert int
    const hour1 = timeStr1.split(":")[0] * 1;
    const minute1 = timeStr1.split(":")[1] * 1;
    const hour2 = timeStr2.split(":")[0] * 1;
    const minute2 = timeStr2.split(":")[1] * 1;

    let isEarlier = -1;
    if (hour1 < hour2) {
      isEarlier = -1;
    } else if (hour1 === hour2) {
      if (minute1 < minute2) {
        isEarlier = -1;
      } else if (minute1 === minute2) {
        isEarlier = 0;
      } else {
        isEarlier = 1;
      }
    } else {
      isEarlier = 1;
    }

    return isEarlier;
  }


  static byteArrayToInt(data, position) {
    return (0xff & data[position]) | (0xff & data[position + 1]) << 8 | (0xff & data[position + 2]) << 16 | (0xff & data[position + 3]) << 24;
  }

  static eventRead(aItem) {
    let params = {
      "did": Device.deviceID,
      "fileId": aItem.fileId,
      "offset": aItem.offset ? aItem.offset : 0
    };
    API.instance().post('/common/app/markRead', 'business.smartcamera', params).then((ret) => {
      console.log(TAG, "eventRead success", ret);
    })
      .catch((err) => {
        console.log(TAG, "eventRead failed", err);
      });
  }

  /*
  aDate: end time
  aRange: fetch range from now//current normal 7 days vip 30 days;
  
  Asc Order
  arg: bgn end
  return bgn[ev ev, ev)endtime           end
  
  Desc Order
  arg: bgn end
  return bgn           endtime(ev ev, ev]end
  */
  static getAllEvent(aDate, aRange, aEvent = 'Default', aIsMore = false, aLimit = 20, aPlayCfg = null, type = CldDldTypes.Files, aOrder = Order.Desc) {
    let bgn = null;
    let end = null;
    let now = new Date();
    let date = new Date(aDate);
    
    if (aIsMore) {
      if (Order.Desc == aOrder) {
        if (aRange == 0) {
          bgn = dayjs(aDate).hour(0).minute(0).second(0).subtract(aRange, "days");
        } else {
          bgn = dayjs().hour(0).minute(0).second(0).subtract(aRange, "days");
        }
        end = dayjs(aDate);
      } else {
        bgn = dayjs(aDate);
        end = dayjs();
      }
    } else {
      if (Order.Desc == aOrder) {
        // now - range
        if (aRange == 0) {
          bgn = dayjs(aDate).hour(0).minute(0).second(0).subtract(aRange, "days");
        } else {
          bgn = dayjs().hour(0).minute(0).second(0).subtract(aRange, "days");
        }
        end = dayjs(aDate).hour(23).minute(59).second(59);      
      } else {
        bgn = dayjs(aDate).add(1, "days").hour(0).minute(0).second(0);
        end = dayjs();   
      }
    }
    console.log(TAG, "getAllEvent for", aDate, "order", aOrder, "isMore", aIsMore, "ret", bgn.format(), "~", end.format());
    let endTime = end.valueOf();
    let beginTime = bgn.valueOf();
    let params = {
      "model": Device.model,
      "eventType": aEvent,
      "beginTime": beginTime,
      "endTime": endTime,
      "limit": aLimit,
      "needMerge": true,
      "doorBell": false,
      "sortType": aOrder,
      "did": Device.deviceID
    };
    if (type == CldDldTypes.Files) {
      return this.requestFiles(params, aPlayCfg);
    } else {
      return this.requestEvents(params, aPlayCfg);
    }
  }



  static getEventList(date, event = 'Default', isMore = false, limit = 20, aPlayCfg = null, type = CldDldTypes.Files, aOrder = Order.Desc) {
    let now = new Date();
    let isToday = Math.floor(now.getTime() / DayInMilli) == Math.floor(date.getDate() / DayInMilli);
    let bgn = null;
    let end = null;

    bgn = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
    end = isToday ? now : new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
    if (isMore) {
      end = date;
    }
    let beginTime = bgn.getTime();
    let endTime = end.getTime();
    let params = {
      "model": Device.model,
      "eventType": event,
      "beginTime": beginTime,
      "endTime": endTime,
      "limit": limit,
      "needMerge": true,
      "doorBell": false,
      "sortType": aOrder,
      "did": Device.deviceID
    };
    if (type == CldDldTypes.Files) {
      return this.requestFiles(params, aPlayCfg);
    } else {
      return this.requestEvents(params, aPlayCfg);
    }

  }

  static getEventsByFileId(did, model, fileId, isAlarm, aPlayCfg = null, evType = "Default") {
    console.log(TAG, "getEventsByFileId");
    let params = {
      'did': did,
      'model': model,
      'isAlarm': isAlarm,
      'fileId': fileId
    };
    return this.requestEventByFileId(params, aPlayCfg, evType);
  }

  static async requestFiles(aParams, aPlayCfg) {
    // console.log(TAG, "requestEvents", aParams);
    let ret = await API.instance().get('/common/app/get/cloudlist', 'business.smartcamera', aParams);
    if (ret.code != 0 || ret.data == null) {
      throw null;
    } else {
      let playUnits = ret.data.thirdPartPlayUnits;
      let eventItems = [];
      let faceMap = {};
      let faceArr = [];
      for (let i = 0; i < playUnits.length; i++) {
        let unit = playUnits[i];
        console.log(TAG, "unit type", unit.eventType, "@", this.getMoment(unit.createTime / 1000).format("MMMD h:mm:ss"));
        let item = {
          createTime: unit.createTime,
          eventTime: DateFormatter.instance().format(unit.createTime),
          type: unit.eventType,
          desc: this.getDescFromType(unit.eventType),
          imgStoreId: unit.imgStoreId,
          fileId: unit.fileId,
          isRead: unit.isRead,
          offset: unit.offset,
          duration: unit.duration, // only for file list
          isAlarm: unit.isAlarm,
          playCfg: aPlayCfg
        };
        eventItems.push(item);

        if (this.hasFaceInfo(unit.eventType)) {
          faceMap[unit.fileId] = item;
          faceArr.push(unit.fileId);
        }
      }

      if (faceArr.length > 0) {
        try {
          let metaRet = await this.getFileMetas(faceArr);
          for (let meta of metaRet.fileIdMetaResults) {
            let fid = meta.fileId;
            // console.log(TAG, "meta info", meta);
            let faceId = meta.faceInfoMetas[0].faceId;
            let faceM = meta.faceInfoMetas[0];
            let itemWithFace = faceMap[fid];
            itemWithFace.faceInfo = { faceId: faceM.faceId, name: faceM.matched ? faceM.figureName : null };
            let extraDes = this.getDescWithFaceFromType(itemWithFace.type, itemWithFace.faceInfo);
            if (extraDes != null) {
              itemWithFace.desc = extraDes;
            }
          }
        } catch (aErr) {
          console.log(TAG, "get faceId err", aErr);
        }
      }

      let data = {
        hasMore: ret.data.isContinue,
        items: eventItems,
        nextTime: new Date(ret.data.nextTime)
      };
      // console.log(TAG, "requestEvents complete");
      return data;
    }
  }

  static async requestEvents(aParams, aPlayCfg) {
    // console.log(TAG, "requestEvents", aParams);
    let ret = await API.instance().get('/common/app/get/eventlist', 'business.smartcamera', aParams);
    if (ret.code != 0 || ret.data == null) {
      throw null;
    } else {
      let playUnits = ret.data.thirdPartPlayUnits;
      let eventItems = [];
      let faceMap = {};
      let faceArr = [];
      for (let i = 0; i < playUnits.length; i++) {
        let unit = playUnits[i];
        console.log(TAG, "unit type", unit.eventType, "@", this.getMoment(unit.createTime / 1000).format("MMMD h:mm:ss"));
        let item = {
          createTime: unit.createTime,
          eventTime: DateFormatter.instance().format(unit.createTime),
          type: unit.eventType,
          desc: this.getDescFromType(unit.eventType),
          imgStoreId: unit.imgStoreId,
          fileId: unit.fileId,
          isRead: unit.isRead,
          offset: unit.offset,
          isAlarm: unit.isAlarm,
          playCfg: aPlayCfg
        };
        eventItems.push(item);

        if (this.hasFaceInfo(unit.eventType)) {
          faceMap[`${ unit.fileId }:${ unit.offset }`] = item;
          faceArr.push(unit.fileId);
        }
      }

      if (faceArr.length > 0) {
        try {
          let metaRet = await this.getFileMetas(faceArr);
          for (let meta of metaRet.fileIdMetaResults) {
            let fid = meta.fileId;
            // console.log(TAG, "meta info", meta);
            let faceId = meta.faceInfoMetas[0].faceId;
            let faceM = meta.faceInfoMetas[0];
            let itemWithFace = faceMap[`${ fid }:${ meta.offset }`];
            itemWithFace.faceInfo = { faceId: faceM.faceId, name: faceM.matched ? faceM.figureName : null, figureId: faceM.figureId };
            let extraDes = this.getDescWithFaceFromType(itemWithFace.type, itemWithFace.faceInfo);
            if (extraDes != null) {
              itemWithFace.desc = extraDes;
            }
          }
        } catch (aErr) {
          console.log(TAG, "get faceId err", aErr);
        }
      }

      let data = {
        hasMore: ret.data.isContinue,
        items: eventItems,
        nextTime: new Date(ret.data.nextTime)
      };
      // console.log(TAG, "requestEvents complete");
      return data;
    }
  }

  static async requestEventByFileId(aParams, aPlayCfg, evType) {
    console.log(TAG, "requestEvents", aParams);
    let ret = await API.instance().get('/common/app/get/fileIdEvents', 'business.smartcamera', aParams);
    console.log(TAG, "requestEvents", ret);
    if (ret.code != 0 || ret.data == null) {
      console.log('requestEventByFileId err', ret.code);
      throw null;
    } else {
      let playUnits = ret.data.thirdPartPlayUnits;
      let eventItems = [];
      let faceMap = {};
      let faceArr = [];
      console.log(TAG, 'event count: ', playUnits.length);
      for (let i = 0; i < playUnits.length; i++) {
        let unit = playUnits[i];
        // console.log(TAG, "unit type", unit.eventType, "@", this.getMoment(unit.createTime / 1000).format("MMMD h:mm:ss"), 'evType', evType);
        if (evType.indexOf('Default') == -1 && unit.eventType.indexOf(evType) == -1) {
          continue;
        }
        let item = {
          createTime: unit.createTime,
          eventTime: this.getMoment(unit.createTime / 1000).format("HH:mm:ss"),
          eventTime2: unit.createTime,
          type: evType.indexOf('Default') == -1 ? evType : unit.eventType,
          desc: this.getDescFromType(unit.eventType),
          imgStoreId: unit.imgStoreId,
          fileId: unit.fileId,
          isRead: unit.isRead,
          offset: unit.offset,
          duration: unit.duration, // only for file list
          isAlarm: unit.isAlarm,
          playCfg: aPlayCfg
        };
        eventItems.push(item);

        if (this.hasFaceInfo(unit.eventType)) {
          faceMap[unit.offset] = item;
          faceArr.push(unit.fileId);
        }

      }
      console.log(TAG, 'event count after filter', evType, eventItems.length);
      eventItems.sort((x, y) => {
        if (x.offset > y.offset) {
          return 1;
        } else {
          return -1;
        }
      });

      let lasttime = -1;
      for (let i = 0; i < eventItems.length; i++) {
        let item = eventItems[i];
        let tDif = 0.0;
        if (lasttime != -1) {
          tDif = (item.eventTime2 - lasttime) / 1.0;
        }
        lasttime = item.eventTime2;
        console.log('faceaa', 'offset:', item.offset, 'dif', tDif, 'eventTime', item.createTime);
      }

      if (faceArr.length > 0) {
        try {
          let metaRet = await this.getFileMetas(faceArr);
          for (let meta of metaRet.fileIdMetaResults) {
            let offset = meta.offset;
            // console.log(TAG, "meta info", meta);
            let faceId = meta.faceInfoMetas[0].faceId;
            let faceM = meta.faceInfoMetas[0];
            let itemWithFace = faceMap[offset];
            itemWithFace.faceInfo = { faceId: faceM.faceId, name: faceM.matched ? faceM.figureName : null, figureId: faceM.figureId };
            let extraDes = this.getDescWithFaceFromType(itemWithFace.type, itemWithFace.faceInfo);
            if (extraDes != null) {
              itemWithFace.desc = extraDes;
            }
          }
        } catch (aErr) {
          console.log(TAG, "get faceId err", aErr);
        }
      }

      let data = {
        hasMore: ret.data.isContinue,
        items: eventItems,
        nextTime: new Date(ret.data.nextTime)
      };
      // console.log(TAG, "requestEvents complete");
      return data;
    }
  }

  static async requestEventTypeByFileId(aParams) {
    console.log(TAG, "requestEventTypeByFileId", aParams);
    let ret = await API.instance().get('/common/app/get/fileIdEvents', 'business.smartcamera', aParams);
    console.log(TAG, "requestEventTypeByFileId", ret);
    if (ret.code != 0 || ret.data == null) {
      console.log('requestEventTypeByFileId err', ret.code);
      throw null;
    } else {
      let playUnits = ret.data.thirdPartPlayUnits;
      let eventTypes = [];
      for (let i = 0; i < playUnits.length; i++) {
        let type = playUnits[i].eventType;
        if (eventTypes.indexOf(type) != -1) {
          eventTypes.push(unit.eventType);
        }
      }

      let data = {
        types: eventTypes,
        fileIDs: params.fileId
      };
      // console.log(TAG, "requestEvents complete");
      return data;
    }
  }

  static getFileMetas(aFileIds) {
    return new Promise((resolve, reject) => {
      API.instance().get("/miot/camera/app/v1/get/fileIdMetas",
        "business.smartcamera",
        { did: Device.deviceID, model: Device.model, fileIds: { fileIds: aFileIds } })
        .then((result) => {
          if (result.code != 0 || result.data == null) {
            reject(null);
          }
          resolve(result.data);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }


  static getFaceImgUrl(aFaceId) {

    return new Promise((resolve, reject) => {
      Service.miotcamera.getCommonImgWithParams(aFaceId,
        JSON.stringify({ prefix: "business.smartcamera", method: "GET", path: "/miot/camera/app/v1/get/face/img" }),
        JSON.stringify({ did: Device.deviceID, faceId: aFaceId, model: Device.model }))
        .then((aRet) => {
          resolve({ uri: `file://${ aRet }` });
        }).catch((err) => {
          reject(err);
        });
    });
  }

  // noFail
  static reportFaceRecError(aEv, type) {
    return new Promise((resolve, reject) => {
      API.instance().post("/miot/camera/app/v1/feedback",
        "business.smartcamera",
        { did: Device.deviceID, model: Device.model, fileId: aEv.fileId, isVisible: true, type: type })
        .then((aRet) => {
          resolve(aRet);
        }).catch((err) => {
          resolve(err);
        });
    });
  }

  static hasFaceInfo(aType) {
    return aType != null && aType.indexOf("Face") != -1;
  }
  
  static getTypeByOrder(aType, aOrder) {
    let pType = aType;
    if (aType != null && aType.indexOf(":") != 1) {
      let tArr = aType.split(":");
      pType = tArr.reduce((aPV, aCV) => {
        if (aOrder[aPV] > aOrder[aCV]) {
          return aPV;
        } else {
          return aCV;
        }
      });
    }
    return pType;
  }

  static getPrimaryType(aType) {
    return this.getTypeByOrder(aType, EvOrder);
  }
  
  static getPrimaryIcoType(aType) {
    return this.getTypeByOrder(aType, EvIcoOrder);
  }
  
  static getDescWithFaceFromType(aType, aFaceInfo) {
    let pType = this.getPrimaryType(aType);
    let ret = EvMap[pType];
    if (ret != null && ret.des_with_face != null) {
      if (aFaceInfo.name != null) {
        return this.fmtStr(ret.des_with_face, aFaceInfo.name);
      } else {
        return this.fmtStr(ret.des_with_face, LocalizedStrings["alarm_unknown_face"]);
      }
    }
    return null;
  }

  static getDescFromType(type) {
    let pType = this.getPrimaryType(type);
    let ret = EvMap[pType];
    if (ret) {
      return ret.des;
    } else {
      return LocalizedStrings.alarm_event_pass;
    }
  }

  static getIconFromType(type, faceName = null) {
    let pType = this.getPrimaryIcoType(type);
    if (pType && pType.indexOf('Face') != -1 && faceName) {
      pType = "KnownFace";
    }
    let ret = EvMap[pType];
    if (ret) {
      return ret.icon.norm;
    } else {
      return DefaltIc;
    }
  }

  static getSmallIconFromType(type) {
    let pType = this.getPrimaryIcoType(type);
    let ret = EvMap[pType];
    if (ret) {
      return ret.icon.small;
    } else {
      return null;
    }
  }

  static getActiveColorFromType(type) {
    let pType = this.getPrimaryIcoType(type);
    let ret = EvMap[pType];
    if (ret) {
      return ret.textActive;
    } else {
      return "#4882FF";
    }
  }

  static isLocalThumb(aUrl, aType = "video") {
    const ct = this.isAndroid() ? "content:"
      : ("image" == aType) ? "miotph:" : "miotvideo:";
    return (aUrl.substr(0, ct.length) == ct);
  }

  static isLocalVideo(aUrl) {
    const ct = this.isAndroid() ? "content:" : "file:";
    return (aUrl.substr(0, ct.length) == ct);
  }



  static getVideoUrl(item) {
    if (this.isLocalVideo(item.fileId)) {
      return Promise.resolve(item.fileId);
    } else {
      return new Promise((resolve, reject) => {
        Service.miotcamera.getVideoFileUrl(item.fileId, item.isAlarm, "H265")// codec is not used at all
          .then((res) => {
            resolve(res);
          }).catch((err) => {
            reject(err);
          });
      });
    }
  }

  static deleteVideo(fileIDs = []) {
    let params = {
      fileIds: {
        fileIds: fileIDs
      }
    };
    return new Promise((resolve, reject) => {
      API.instance().post('/common/app/v2/delete/files', 'business.smartcamera', params)
        .then((result) => {
          resolve(result);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }


  static async sleep(aTime) {
    await new Promise((aResol) => { setTimeout(aResol, aTime); });
  }

  static getNonce() {
    let now = Date.now();
    // console.log(`now${ now }`);
    return `${ now }${ Math.random(now) }`;
  }


  static fetchVipStatus() {
    return new Promise((resolve, reject) => {
      API.instance().get("/miot/camera/app/v1/vip/status", "business.smartcamera")
        .then((result) => {
          if (result.code != 0 || result.data == null) {
            reject(null);
          }
          resolve(result.data);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  static fetchCloudCapacity() {
    return new Promise((resolve, reject) => {
      API.instance().get("/common/app/v1/capacity", "business.smartcamera", { did: Device.deviceID, region: "CN" })
        .then((result) => {
          if (result.code != 0 || result.data == null) {
            reject(null);
          }
          resolve(result.data);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  static getMoment(aTsInSec) {
    return dayjs.unix(aTsInSec).locale(this.getLanguage());

  }

  static _formatSize(bytes) {
    console.log(bytes);
    return bytes < 1024 ? `${ bytes }B` : (
      bytes < 1024 * 1024 ? `${ (bytes / 1024).toFixed(2) }K` : (
        bytes < 1024 * 1024 * 1024 ? `${ (bytes / 1024 / 1024).toFixed(2) }M` :
          `${ (bytes / 1024 / 1024 / 1024).toFixed(2) }G`
      )
    );
  }

  static async getExistFigure(aFigureN) {
    let queryParams = {
      did: Device.deviceID,
      model: Device.model,
      figureName: aFigureN
    };
    let ret = null;
    try {
      ret = await API.instance().get(CloudApi.QueryFigure, CameraDomain, queryParams);
    } catch (err) {
      console.log(TAG, "no exiting find", err);
    }
    if (ret && "ok" == ret.result.toLowerCase() && ret.data && ret.data.figureId) {
      return ret.data.figureId;
    } else {
      return null;
    }
  }
  
  static async addFaceToExisting(aFigureId, aFaceId) {
    let addParams = {
      did: Device.deviceID,
      model: Device.model,
      faceId: aFaceId,
      figureId: aFigureId
    };
    console.log(TAG, "commentFace find add face to existing");
    return await API.instance().post(CloudApi.AddFace, CameraDomain, addParams);
  }
  
  static async commentFace(aFigureN, aFaceId) {
    let api = API.instance();
    let queryParams = {
      did: Device.deviceID,
      model: Device.model,
      figureName: aFigureN
    };
    let curApi = null;
    let ret = null;
    let existFigureId = await this.getExistFigure(aFigureN);
    
    if (existFigureId != null) {
      await this.addFaceToExisting(existFigureId, aFaceId);
    } else {
      let figParams = {
        did: Device.deviceID,
        model: Device.model,
        figureInfo: aFigureN,
        figureName: aFigureN
      };
      // console.log('faceaa', 'enter commentface');
      curApi = CloudApi.AddFigure;
      ret = await api.post(curApi, CameraDomain, figParams);
      if ("ok" == ret.result.toLowerCase()) {
        let faceParams = {
          did: Device.deviceID,
          model: Device.model,
          figureId: ret.data.figureId,
          faceId: aFaceId
        };
        // console.log('faceaa', 'addFigure ok, id: ', ret.data.figureId);
        curApi = CloudApi.AddFace;
        ret = await api.post(curApi, CameraDomain, faceParams);
        // console.log('faceaa', 'AddFace result: ', ret);
        return ret;  
      } else {
        let err = `${ curApi } failed ${ ret }`;
        // console.log(TAG, 'faceaa', "commentFace error ", err);
        throw err;
      }  
    }
  }
  
  static async modifyFaceComment(aFigureId, aFaceId, aFigureN) {
    let ret = null;
    let existFigureId = await this.getExistFigure(aFigureN);
    if (existFigureId != null && aFigureId != existFigureId) {
      let delParams = {
        did: Device.deviceID,
        model: Device.model,
        faceId: aFaceId,
        figureId: aFigureId
      };
      await API.instance().post(CloudApi.DelFace, CameraDomain, delParams);
      this.addFaceToExisting(existFigureId, aFaceId);
    } else {
      let modParams = {
        did: Device.deviceID,
        model: Device.model,
        figureId: aFigureId,
        figureInfo: aFigureN,
        figureName: aFigureN
      };
      console.log(TAG, "start modify figure");
      ret = await API.instance().post(CloudApi.ModifyFigure, CameraDomain, modParams); 
      console.log(TAG, "modify figure complete"); 
    }
    
    return ret;
  }
  
  static async getAllFigure() {
    let delParams = {
      did: Device.deviceID,
      model: Device.model
    };
    let ret = await API.instance().get(CloudApi.QueryAllFigure, CameraDomain, delParams);
    if (ret != null && 0 == ret.code) {
      let data = ret.data;
      if (data != null && data.figureInfos != null) {
        let retDat = [];
        for (let fInf of data.figureInfos) {
          let faceUrl = null;
          let name = fInf.figureName;
          try {
            faceUrl = await this.getFaceImgUrl(fInf.coverFaceId);  
            console.log(TAG, "get face:", name, "with", faceUrl);
          } catch (aExp) {
            console.log(TAG, "get faceUrl failed with", fInf.coverFaceId);
          }
          
          retDat.push({ name, faceUrl });
        }
        return retDat;
      }
    } else {
      throw "empty result";
    }
    return ret;
  }

  static isDark() {
    // return "dark" == DarkMode.getColorScheme();
    return false;
  }

  static isToday(date) {
    let now = dayjs(new Date());
    let TODAY = now.clone().startOf('day');
    let isToday = dayjs(date).isSame(TODAY, 'd');
    return isToday;
  }

  static isYestoday(date) {
    let now = dayjs(new Date());
    let YESTERDAY = now.clone().subtract(1, 'days').startOf('day');
    let isYestoday = dayjs(date).isSame(YESTERDAY, 'd');
    return isYestoday;
  }
}
